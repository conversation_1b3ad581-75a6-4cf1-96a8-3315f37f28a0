using System;
using DG.Tweening;
using FairyGUI;
using TMPro;
using UnityEngine;
using UnityEngine.U2D;

public class Tile3D : MonoBehaviour
{
    public const int TYPE_EMPTY = 0;
    public const int TYPE_BLOCK = 1;
    public const int TYPE_BLOCK_END = 99;
    public const int TYPE_QUESTION = 100;
    public const int TYPE_FREEZE1 = 201;
    public const int TYPE_FREEZE2 = 202;
    public const int TYPE_FREEZE3 = 203;
    public const int TYPE_CHAIN = 300;
    public const int TYPE_WOOD = 400;
    public const int TYPE_STONE = 500;
    public const int TYPE_FIRECRACKER = 600;

    public GameObject cube;
    // public TextMeshPro txt;
    public GameObject chainGo;
    public GameObject topIcon;

    public Action OnClick;
    public Color selectedColor = Color.green;
    public Color promptColor = Color.cyan;

    [NonSerialized] public bool isSelected;
    [NonSerialized] public bool isPrompt;
    [NonSerialized] public bool isShuffling;
    [NonSerialized] public int freezeLayer;
    [NonSerialized] public bool isYoyo;
    public BoardCell Block
    {
        get { return _block; }
    }

    private Material _material;
    private Color _originalCubeColor;
    private Color _darkColor;

    private BoardCell _block;
    private Vector3 _initScale;

    private Material _topIconMaterial;
    private MeshRenderer _meshRenderer;
    private MeshRenderer _topIconRenderer;

    // 预设材质引用
    private Material _defaultMaterial;
    private Material _selectedMaterial;
    private Material _promptMaterial;
    private Material _darkMaterial;
    private Material _whiteMaterial;
    private Material _defaultTopIconMaterial;
    private Material _darkTopIconMaterial;

    private Texture skinTexture;
    private const float POP_HEIGHT = 0.2f;
    public int type;
    private bool isOnlyTopIcon;

    private PoolItem _selectedEffect;
    private PoolItem _firecrackerEffect;
    private void Awake()
    {
        // 获取渲染器组件
        _meshRenderer = GetComponentInChildren<MeshRenderer>();
        _topIconRenderer = topIcon.GetComponent<MeshRenderer>();

        // 获取所有预设材质
        _defaultMaterial = BattleResources.Inst.GetMaterial("TileMaterial_Default");
        _selectedMaterial = BattleResources.Inst.GetMaterial("TileMaterial_Selected");
        _promptMaterial = BattleResources.Inst.GetMaterial("TileMaterial_Prompt");
        _darkMaterial = BattleResources.Inst.GetMaterial("TileMaterial_Dark");
        _whiteMaterial = BattleResources.Inst.GetMaterial("TileMaterial_White");
        _defaultTopIconMaterial = BattleResources.Inst.GetMaterial("TopIconMaterial_Default");
        _darkTopIconMaterial = BattleResources.Inst.GetMaterial("TopIconMaterial_Dark");

        // 设置默认材质
        _material = _defaultMaterial;
        if (_material != null)
        {
            _meshRenderer.material = _material;
            _originalCubeColor = _material.color;
        }
        else
        {
            // 如果预设材质不存在，回退到原来的方式
            _material = _meshRenderer.material;
            _originalCubeColor = _material.color;
        }

        _topIconMaterial = _defaultTopIconMaterial;
        if (_topIconMaterial != null)
        {
            _topIconRenderer.material = _topIconMaterial;
        }
        else
        {
            // 如果预设材质不存在，回退到原来的方式
            _topIconMaterial = _topIconRenderer.material;
        }

        _darkColor = new Color(_originalCubeColor.r * 0.6f, _originalCubeColor.g * 0.6f, _originalCubeColor.b * 0.6f, 1);
        _initScale = topIcon.transform.localScale;
    }

    private void SetTileMaterial(Material material)
    {
        if (material != null)
        {
            _material = material;
            _meshRenderer.material = material;
        }
    }

    private void SetTopIconMaterial(Material material)
    {
        if (material != null)
        {
            _topIconMaterial = material;
            _topIconRenderer.material = material;
        }
    }

    private void SetTileMaterialWithTexture(Texture texture, Color color)
    {
        var material = BattleResources.Inst.GetOrCreateMaterialWithTexture("TileMaterial_Default", texture, color);
        SetTileMaterial(material);
    }

    private void SetTopIconMaterialWithProperties(Texture texture, int width, int height, int index)
    {
        // 为TopIcon创建带有特定属性的材质
        var material = BattleResources.Inst.GetOrCreateMaterialWithTexture("TopIconMaterial_Default", texture, Color.white);
        if (material != null)
        {
            material.SetInt("_Width", width);
            material.SetInt("_Height", height);
            material.SetInt("_Index", index);
            SetTopIconMaterial(material);
        }
    }

    public void SetData(bool isOnlyTopIcon, BoardCell block, Texture skinTexture)
    {
        _block = block;
        this.skinTexture = skinTexture;
        this.isOnlyTopIcon = isOnlyTopIcon;

        // 如果在Awake时材质还没有加载完成，在这里再次尝试获取
        if (_defaultMaterial == null)
        {
            _defaultMaterial = BattleResources.Inst.GetMaterial("TileMaterial_Default");
            _selectedMaterial = BattleResources.Inst.GetMaterial("TileMaterial_Selected");
            _promptMaterial = BattleResources.Inst.GetMaterial("TileMaterial_Prompt");
            _darkMaterial = BattleResources.Inst.GetMaterial("TileMaterial_Dark");
            _whiteMaterial = BattleResources.Inst.GetMaterial("TileMaterial_White");
            _defaultTopIconMaterial = BattleResources.Inst.GetMaterial("TopIconMaterial_Default");
            _darkTopIconMaterial = BattleResources.Inst.GetMaterial("TopIconMaterial_Dark");

            if (_defaultMaterial != null)
            {
                _material = _defaultMaterial;
                _meshRenderer.material = _material;
                _originalCubeColor = _material.color;
                _darkColor = new Color(_originalCubeColor.r * 0.6f, _originalCubeColor.g * 0.6f, _originalCubeColor.b * 0.6f, 1);
            }

            if (_defaultTopIconMaterial != null)
            {
                _topIconMaterial = _defaultTopIconMaterial;
                _topIconRenderer.material = _topIconMaterial;
            }
        }

        SetIcon(block.type);
        if (IsQuestion)
        {
            cube.transform.rotation = Quaternion.Euler(0, 0, 180);
        }
        else if (IsFirecracker)
        {
            _originalCubeColor = new Color(0.91f, 0.435f, 0.435f);
            // 为爆竹创建特殊颜色的材质
            SetTileMaterialWithTexture(BattleResources.Inst.GetBlockTexture(TYPE_BLOCK), _originalCubeColor);
            YoyoIcon(1.2f, 0.9f);
            ShowFirecrackerEffect();
        }
    }

    private void SetIcon(int type)
    {
        this.type = type;
        if (type == TYPE_EMPTY)
        {
            gameObject.SetActive(false);
            return;
        }

        chainGo.SetActive(IsChain);

        if (type < TYPE_BLOCK_END || IsChain)
        {
            var iconIndex = IsChain ? type - TYPE_CHAIN : type;
            SetTileMaterialWithTexture(BattleResources.Inst.GetBlockTexture(TYPE_BLOCK), _originalCubeColor);

            SetTopIconMaterialWithProperties(skinTexture, 6, 6, iconIndex - 1);
        }
        else if (IsQuestion)
        {
            SetTileMaterialWithTexture(BattleResources.Inst.GetBlockTexture(TYPE_BLOCK), _originalCubeColor);

            topIcon.SetActive(false);
        }
        else if (IsFirecracker)
        {
            SetTileMaterialWithTexture(BattleResources.Inst.GetBlockTexture(TYPE_BLOCK), _originalCubeColor);
            var firecrackerTexture = BattleResources.Inst.GetBlockTexture(TYPE_FIRECRACKER);

            SetTopIconMaterialWithProperties(firecrackerTexture, 1, 1, 0);
        }
        else
        {
            SetTileMaterialWithTexture(BattleResources.Inst.GetBlockTexture(type), Color.white);
            topIcon.SetActive(false);
        }
    }

    public bool IsQuestion
    {
        get
        {
            return _block.type > TYPE_QUESTION && _block.type < TYPE_QUESTION + 100;
        }
    }

    public bool IsChain
    {
        get
        {
            return _block.type > TYPE_CHAIN && _block.type < TYPE_CHAIN + 100;
        }
    }
    public bool IsWood
    {
        get
        {
            return _block.type == TYPE_WOOD;
        }
    }
    public bool IsStone
    {
        get
        {
            return _block.type == TYPE_STONE;
        }
    }

    public bool IsFirecracker
    {
        get
        {
            return _block.type == TYPE_FIRECRACKER;
        }
    }

    public bool IsFreeze
    {
        get
        {
            return _block.type >= TYPE_FREEZE1 && _block.type <= TYPE_FREEZE3;
        }
    }

    /// <summary>
    /// 可被点击选中
    /// </summary>
    /// <value></value>
    public bool CanSelect
    {
        get
        {
            return _block.type > TYPE_EMPTY && _block.type < TYPE_BLOCK_END || IsFirecracker;
        }
    }

    /// <summary>
    /// 可被洗牌
    /// </summary>
    /// <value></value>
    public bool CanShuffle
    {
        get
        {
            return _block.type > TYPE_EMPTY && _block.type < TYPE_BLOCK_END || IsFirecracker;
        }
    }

    /// <summary>
    /// 可移动（被重力方向影响）
    /// </summary>
    /// <value></value>
    public bool CanMove
    {
        get
        {
            return _block.type != TYPE_STONE && _block.type != TYPE_FREEZE1 && _block.type != TYPE_FREEZE2 && _block.type != TYPE_FREEZE3;
        }
    }

    /// <summary>
    /// 可连线
    /// </summary>
    /// <value></value>
    public bool CanMatch
    {
        get
        {
            return _block.type >= TYPE_BLOCK && _block.type <= TYPE_BLOCK_END || IsQuestion || IsChain || IsFirecracker;
        }
    }

    public bool TryOpenTile()
    {
        if (IsQuestion)
        {
            _block.type -= TYPE_QUESTION;
            SetIcon(_block.type);
            cube.transform.DORotate(new Vector3(0, 0, 180), 0.3f, RotateMode.WorldAxisAdd);
            return true;
        }
        else if (IsChain)
        {
            _block.type -= TYPE_CHAIN;
            SetIcon(_block.type);
            return true;
        }
        else if (IsFreeze)
        {
            switch (_block.type)
            {
                case TYPE_FREEZE3:
                    _block.type = TYPE_FREEZE2;
                    break;
                case TYPE_FREEZE2:
                    _block.type = TYPE_FREEZE1;
                    break;
                case TYPE_FREEZE1:
                    _block.type = TYPE_EMPTY;
                    break;
            }
            SetIcon(_block.type);
            return true;
        }
        return false;
    }

    public bool RemoveSpecialTile()
    {
        var needRemoveTile = false;
        if (IsQuestion)
        {
            _block.type -= TYPE_QUESTION;
            SetIcon(_block.type);
            cube.transform.DORotate(new Vector3(0, 0, 180), 0.3f, RotateMode.WorldAxisAdd);
        }
        else if (IsChain)
        {
            _block.type -= TYPE_CHAIN;
            SetIcon(_block.type);
        }
        else if (IsFirecracker)
        {
            //不用处理
        }
        else
        {
            _block.type = TYPE_EMPTY;
            SetIcon(_block.type);
            needRemoveTile = true;
        }
        return needRemoveTile;
    }


    public void StartShuffle()
    {
        isShuffling = true;
        isSelected = false;
        transform.DOKill(true);
        transform.rotation = Quaternion.identity;
        transform.localScale = Vector3.one;
        SetCurrentMaterial();
        StopYoyoIcon();

        // 隐藏选中特效
        HideSelectedEffect();
    }
    public void EndShuffle()
    {
        isShuffling = false;
    }

    public void OnMatch()
    {
        transform.DOScale(0.1f, 0.1f);
    }

    public void OnUnmatch()
    {
        transform.rotation = Quaternion.Euler(0, -15, 0);
        transform.DORotate(new Vector3(0, 15, 0), 0.08f)
            .SetLoops(3, LoopType.Yoyo)
            .OnComplete(() => transform.rotation = Quaternion.identity);
    }


    private bool inDoubleClick = false;
    public void OnDoubleClick(float duration = 0.1f)
    {
        if (inDoubleClick || isShuffling)
            return;
        inDoubleClick = true;

        cube.transform.localPosition = new Vector3(0, POP_HEIGHT, 0);

        transform.rotation = Quaternion.Euler(0, -15, 0);
        SetTileMaterial(_selectedMaterial);
        transform.DORotate(new Vector3(0, 15, 0), duration)
            .SetLoops(6, LoopType.Yoyo)
            .OnComplete(() =>
            {
                inDoubleClick = false;
                transform.rotation = Quaternion.identity;

                var y = isSelected ? POP_HEIGHT : 0;
                cube.transform.localPosition = new Vector3(0, y, 0);
                SetCurrentMaterial();
                if (_selectedEffect != null)
                {
                    _selectedEffect.transform.rotation = Quaternion.identity;
                }
            });
    }

    private void SetCurrentMaterial()
    {
        if (isSelected)
        {
            SetTileMaterial(_selectedMaterial);
        }
        else if (isPrompt)
        {
            SetTileMaterial(_promptMaterial);
        }
        else if (!topIcon.activeInHierarchy)
        {
            SetTileMaterial(_whiteMaterial);
        }
        else
        {
            SetTileMaterial(_defaultMaterial);
        }
    }



    private void YoyoIcon(float scale = 1.1f, float duration = 0.6f)
    {
        if (isYoyo)
            return;
        isYoyo = true;
        topIcon.transform.DOScale(_initScale * scale, duration).SetLoops(-1, LoopType.Yoyo);
    }

    private void StopYoyoIcon()
    {
        if (isSelected || isPrompt || IsFirecracker)
            return;
        isYoyo = false;
        topIcon.transform.DOKill();
        topIcon.transform.localScale = _initScale;
    }

    public void OnPrompt()
    {
        isPrompt = true;
        SetTileMaterial(_promptMaterial);
        YoyoIcon();
    }

    public void OnUnprompt()
    {
        isPrompt = false;
        SetCurrentMaterial();
        StopYoyoIcon();
    }

    public void OnSelect()
    {
        if (isSelected)
            return;
        isSelected = true;
        SetTileMaterial(_selectedMaterial);

        cube.transform.localPosition = new Vector3(0, POP_HEIGHT, 0);
        YoyoIcon();

        // 显示选中特效
        ShowSelectedEffect();
    }

    public void OnDeselect()
    {
        if (!isSelected)
            return;
        isSelected = false;
        SetCurrentMaterial();

        cube.transform.localPosition = Vector3.zero;

        StopYoyoIcon();

        // 隐藏选中特效
        HideSelectedEffect();
    }

    public void OnRemove()
    {
        // 清理爆竹特效
        HideFirecrackerEffect();
        gameObject.SetActive(false);
    }

    private void ShowSelectedEffect()
    {
        if (_selectedEffect != null)
            return;

        _selectedEffect = BattleResources.Inst.GetPoolItem(PoolNames.Pool_EffectTileSelected);
        if (_selectedEffect != null)
        {
            _selectedEffect.transform.position = transform.position;
            _selectedEffect.transform.rotation = Quaternion.identity;
            _selectedEffect.transform.SetParent(transform);
        }
    }

    private void HideSelectedEffect()
    {
        if (_selectedEffect != null)
        {
            _selectedEffect.Release();
            _selectedEffect = null;
        }
    }

    private void ShowFirecrackerEffect()
    {
        if (_firecrackerEffect != null)
            return;

        _firecrackerEffect = BattleResources.Inst.GetPoolItem(PoolNames.Pool_EffectShinePink);
        if (_firecrackerEffect != null)
        {
            _firecrackerEffect.transform.position = transform.position;
            _firecrackerEffect.transform.rotation = Quaternion.identity;
            _firecrackerEffect.transform.SetParent(transform);
        }
    }

    private void HideFirecrackerEffect()
    {
        if (_firecrackerEffect != null)
        {
            _firecrackerEffect.Release();
            _firecrackerEffect = null;
        }
    }

    private void OnDestroy()
    {
        if (topIcon && topIcon.transform)
        {
            topIcon.transform.DOKill();
        }

        // 清理选中特效
        HideSelectedEffect();

        // 清理爆竹特效
        HideFirecrackerEffect();
    }

    public void SetDark()
    {
        // 直接设置暗色材质
        SetTileMaterial(_darkMaterial);
        SetTopIconMaterial(_darkTopIconMaterial);
    }

    public void SetBright()
    {
        // 恢复到当前状态的材质
        SetCurrentMaterial();
        SetTopIconMaterial(_defaultTopIconMaterial);
    }
}
